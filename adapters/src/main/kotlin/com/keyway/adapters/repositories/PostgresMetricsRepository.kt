package com.keyway.adapters.repositories

import com.keyway.adapters.repositories.utils.IdTypeUtils.getSqlColumn
import com.keyway.adapters.repositories.utils.PostgresQueryUtils.getBigDecimal
import com.keyway.adapters.repositories.utils.PostgresQueryUtils.getInt
import com.keyway.adapters.repositories.utils.PostgresQueryUtils.getOptionalInt
import com.keyway.adapters.repositories.utils.PostgresQueryUtils.getString
import com.keyway.adapters.repositories.utils.RentUtils
import com.keyway.adapters.repositories.utils.UnitConditionUtils.getUnitConditionJoin
import com.keyway.adapters.repositories.utils.UnitConditionUtils.getWhere
import com.keyway.core.dto.AskingMetricDto
import com.keyway.core.dto.EffectiveMetricDto
import com.keyway.core.dto.Metric
import com.keyway.core.dto.MetricDto
import com.keyway.core.dto.query.metrics.MetricsFiltersQuery
import com.keyway.core.entities.RentListingType
import com.keyway.core.entities.metric.MetricType
import com.keyway.core.ports.repositories.MetricsRepository
import com.keyway.core.utils.ListingUtils
import com.keyway.kommons.db.SqlClient
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import kotlinx.coroutines.withContext
import java.math.BigDecimal

class PostgresMetricsRepository(
    private val sqlClient: SqlClient,
) : MetricsRepository {
    companion object {
        val groupersMap: Map<MetricType, List<String>> =
            mapOf(
                MetricType.BY_ID to emptyList(),
                MetricType.BEDROOMS to listOf("bedrooms"),
                MetricType.UNIT_MIX to listOf("bedrooms", "bathrooms"),
                MetricType.FLOOR_PLAN to listOf("floor_plan", "bedrooms", "bathrooms"),
                MetricType.UNITS to listOf("type_id", "floor_plan", "bedrooms", "bathrooms", "unit_square_footage"),
            )
    }

    override suspend fun aggregateMetrics(metricsFiltersQuery: MetricsFiltersQuery): List<MetricDto> =
        withContext(Dispatchers.IO) {
            val propertyIdsPlaceholders = metricsFiltersQuery.ids.joinToString(",") { "?" }
            val idColumn = metricsFiltersQuery.idType.getSqlColumn()
            val groupers = listOf(idColumn).plus(groupersMap.getValue(metricsFiltersQuery.type))

            val askingDeferred = async { getAskingMetrics(groupers, idColumn, propertyIdsPlaceholders, metricsFiltersQuery) }
            val effectiveDeferred = async { getEffectiveMetrics(groupers, idColumn, propertyIdsPlaceholders, metricsFiltersQuery) }
            val askingResult = askingDeferred.await()
            val effectiveResult = effectiveDeferred.await()

            val effectiveMap = effectiveResult.associateBy { it.code }
            askingResult.map { ask ->
                MetricDto(ask, effectiveMap[ask.code])
            }
        }

    private fun getAskingMetrics(
        groupers: List<String>,
        idColumn: String,
        propertyIdsPlaceholders: String,
        metricsFiltersQuery: MetricsFiltersQuery,
    ): List<AskingMetricDto> {
        val query =
            """
            WITH 
            filtered_listings AS (
                SELECT
                    rl.id,
                    rl.rent,
                    rl.rent_deposit,
                    ${"rl.unit_square_footage,".takeUnless { groupers.contains("unit_square_footage") } ?: ""}
                    rl.type,
                    ${"rl.type_id,".takeUnless { groupers.contains("type_id") } ?: ""}
                    CASE WHEN rl.date_to >= CURRENT_DATE - INTERVAL '${ListingUtils.ACTIVE_AMOUNT_DAYS}' DAY
                        AND rl.type = 'UNIT' THEN type_id ELSE null END as available,
                     (LEAST(rl.date_to, ?) - 
                        GREATEST(rl.date_from, ?) + 1) as days,
                    ${getGroupersSql(groupers, "rl")}
                FROM rent_listing rl
                ${metricsFiltersQuery.unitCondition?.getUnitConditionJoin() ?: ""}
                WHERE rl.$idColumn IN ($propertyIdsPlaceholders)
                    AND rl.date_from <= ? 
                    AND rl.date_to >= ?
                    ${RentUtils.isActiveSql(tableName = "rl")}
                    ${metricsFiltersQuery.rentListingType?.name?.let { " AND rl.type = '$it' "} ?: "" }
                    ${metricsFiltersQuery.unitCondition?.getWhere() ?: ""}
            )   
              SELECT 
                   ${getGroupersSql(groupers, "filtered_listings")},
                    COUNT(*) as total_records,
                    ${buildMetricsClause().joinToString(",")},
                    ROUND(AVG(days),2) AS average_listing_days,
                    COUNT(DISTINCT type_id) FILTER(WHERE type = '${RentListingType.UNIT.name}') as total_units,
                    COUNT(DISTINCT available) as units_available
                FROM filtered_listings 
                GROUP BY ${getGroupersSql(groupers)}
            
            """.trimIndent()

        return sqlClient.getAll(
            query = query,
            params =
                listOf<Any>()
                    .asSequence()
                    .plus(metricsFiltersQuery.dateTo)
                    .plus(metricsFiltersQuery.dateFrom)
                    .plus(metricsFiltersQuery.ids)
                    .plus(metricsFiltersQuery.dateTo)
                    .plus(metricsFiltersQuery.dateFrom)
                    .toList(),
        ) { result ->
            buildAskingDto(result, idColumn)
        }
    }

    private fun getEffectiveMetrics(
        groupers: List<String>,
        idColumn: String,
        propertyIdsPlaceholders: String,
        metricsFiltersQuery: MetricsFiltersQuery,
    ): List<EffectiveMetricDto> {
        val query =
            """
            WITH 
            filtered_effective AS (
                SELECT 
                    effective.rent,
                    effective.rent_deposit,
                    (LEAST(effective.date_to, ?) - 
                        GREATEST(effective.date_from, ?) + 1) as days,
                    ${getGroupersSql(groupers, "effective")}
                FROM effective_rent effective
                ${metricsFiltersQuery.unitCondition?.getUnitConditionJoin(rentAlias = "effective") ?: ""}
                WHERE effective.$idColumn IN ($propertyIdsPlaceholders)
                    AND effective.date_from <= ? 
                    AND effective.date_to >= ?
                    ${RentUtils.isActiveSql(tableName = "effective")}
                    ${metricsFiltersQuery.rentListingType?.name?.let { " AND effective.type = '$it' "} ?: "" }
                    ${metricsFiltersQuery.unitCondition?.getWhere() ?: ""}
            ) 
                SELECT 
                    ${getGroupersSql(groupers, "filtered_effective")},
                    ${generateDayBasedMetricsClause("rent", "filtered_effective", "effective_rent")}
                FROM filtered_effective
                GROUP BY ${getGroupersSql(groupers, "filtered_effective")}
            
            """.trimIndent()

        return sqlClient.getAll(
            query = query,
            params =
                listOf<Any>()
                    .asSequence()
                    .plus(metricsFiltersQuery.dateTo)
                    .plus(metricsFiltersQuery.dateFrom)
                    .plus(metricsFiltersQuery.ids)
                    .plus(metricsFiltersQuery.dateTo)
                    .plus(metricsFiltersQuery.dateFrom)
                    .toList(),
        ) { result ->
            buildEffectiveDto(result, idColumn)
        }
    }

    private fun getGroupersSql(
        groupers: List<String>,
        table: String? = null,
    ) = """ ${groupers.joinToString(",") { "$table.$it".takeIf { table != null } ?: it }}"""

    private fun buildEffectiveDto(
        result: Map<String, Any>,
        idColumn: String,
    ): EffectiveMetricDto =
        EffectiveMetricDto(
            id = result.getString(idColumn),
            bedrooms = result.getOptionalInt("bedrooms"),
            bathrooms = result.getBigDecimal("bathrooms", 1),
            floorPlan = result["floor_plan"]?.toString(),
            effectiveRent = buildMetric(result, "effective_rent"),
        )

    private fun buildAskingDto(
        result: Map<String, Any>,
        idColumn: String,
    ): AskingMetricDto =
        AskingMetricDto(
            id = result.getString(idColumn),
            totalRecords = result.getInt("total_records"),
            averageListingsDays = result.getBigDecimal("average_listing_days") ?: BigDecimal.ZERO,
            unitsAvailable = result.getInt("units_available"),
            askingRent = buildMetric(result, "rent")!!,
            deposit = buildMetric(result, "rent_deposit"),
            squareFootage = buildMetric(result, "unit_square_footage"),
            bedrooms = result.getOptionalInt("bedrooms"),
            bathrooms = result.getBigDecimal("bathrooms", 1),
            floorPlan = result["floor_plan"]?.toString(),
            totalUnits = result.getInt("total_units"),
            unitId = result["type_id"].toString(),
        )

    private fun buildMetric(
        result: Map<String, Any>,
        prefix: String,
    ): Metric? {
        val min = result.getBigDecimal("${prefix}_min")
        val max = result.getBigDecimal("${prefix}_max")
        val average = result.getBigDecimal("${prefix}_average")
        val median = result.getBigDecimal("${prefix}_median")

        return takeIf { min != null && max != null && average != null && median != null }?.let {
            Metric(min = min!!, max = max!!, average = average!!, median = median!!)
        }
    }

    private fun buildMetricsClause(): List<String> =
        listOf(
            generateDayBasedMetricsClause("rent", "filtered_listings", "rent"),
            generateDayBasedNullableMetricsClause("rent_deposit", "filtered_listings", "rent_deposit"),
            generateNullableMetricsClause("unit_square_footage", "filtered_listings", "unit_square_footage"),
        )

    private fun generateDayBasedMetricsClause(
        grouper: String,
        table: String,
        alias: String,
    ): String =
        """
        ROUND(AVG($table.$grouper), 2) AS ${alias}_average,
        MIN($table.$grouper) AS ${alias}_min,
        ROUND((percentile_cont(0.5) WITHIN GROUP 
            (ORDER BY $table.$grouper))::numeric, 2) AS ${alias}_median,
        MAX($table.$grouper) AS ${alias}_max
        """.trimIndent()

    private fun generateDayBasedNullableMetricsClause(
        grouper: String,
        table: String,
        alias: String,
    ): String =
        """
        ROUND(AVG(CASE WHEN $table.$grouper IS NOT NULL THEN $table.$grouper END), 2) AS ${alias}_average,
        MIN($table.$grouper) AS ${alias}_min,
        ROUND((percentile_cont(0.5) WITHIN GROUP 
            (ORDER BY CASE WHEN $table.$grouper IS NOT NULL THEN $table.$grouper END))::numeric, 
                2) AS ${alias}_median,
        MAX($table.$grouper) AS ${alias}_max
        """.trimIndent()

    private fun generateNullableMetricsClause(
        grouper: String,
        table: String,
        alias: String,
    ): String =
        """
        ROUND(AVG(CASE WHEN $table.$grouper IS NOT NULL THEN $table.$grouper END), 2) AS ${alias}_average,
        MIN($table.$grouper) AS ${alias}_min,
        ROUND((percentile_cont(0.5) WITHIN GROUP 
            (ORDER BY CASE WHEN $table.$grouper IS NOT NULL THEN $table.$grouper END))::numeric, 
                2) AS ${alias}_median,
        MAX($table.$grouper) AS ${alias}_max
        """.trimIndent()
}
